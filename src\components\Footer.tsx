import React, { RefObject, Dispatch, SetStateAction } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Instagram, Linkedin } from "lucide-react";
import { SiX } from "react-icons/si";

const Footer: React.FC<{
	CoreFeaturesRef?: RefObject<HTMLElement>;
	setshowContactUsModal: Dispatch<SetStateAction<boolean>>;
}> = () =>
	// 	{
	// 	CoreFeaturesRef,
	// }
	{
		const navigate = useRouter();
		return (
			<footer className="relative mt-[38px] w-full pt-12 font-inter">
				<svg
					className="absolute inset-0 hidden h-full w-full lg:block"
					preserveAspectRatio="none"
					viewBox="0 0 1323 443"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M1 1.19531V442.195H663.801H1322V1.19531L1309.07 4.66362C886.307 118.029 441.192 118.416 18.2364 5.78525L1 1.19531Z"
						fill="#032F57"
						stroke="black"
					/>
				</svg>
				<svg
					className="absolute inset-0 h-full w-full lg:hidden"
					preserveAspectRatio="none"
					viewBox="0 0 360 889"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M-0.462891 1V967L18.3873 962.123C130.483 933.12 248.316 934.805 359.537 967V1C252.678 68.141 117.318 70.1696 8.49559 6.26106L-0.462891 1Z"
						fill="#032F57"
						stroke="black"
					/>
				</svg>
				<div className="relative z-10 flex w-full items-center justify-center">
					<div className="flex w-full max-w-[1216px] flex-col items-center space-y-12 px-4 py-10">
						<div className="flex w-full flex-col items-center justify-between space-y-6 text-white lg:flex-row lg:space-y-0">
							<img
								src="/assets/images/brand/logo-white.svg"
								className="cursor-pointer"
								onClick={() => navigate.push("/")}
								alt="Migranium white logo"
							/>
							<ul className="flex flex-col items-center space-y-6 font-inter text-base font-medium text-white/70 lg:flex-row lg:space-x-12 lg:space-y-0">
								<Link
									href={"/#features"}
									className="duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Features
								</Link>
								<Link
									href={"/#integrations"}
									className="duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Integrations
								</Link>
								<Link
									href={"/#why-migranium"}
									className="duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Why Migranium
								</Link>
								<Link
									href={"/#impact"}
									className="duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Impact
								</Link>
								<button
									type="button"
									// onClick={() => setshowContactUsModal(true)}
									className="duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Contact Us
								</button>
							</ul>
							<ul className="flex items-center space-x-6 fill-white">
								<a
									href="https://www.instagram.com/migranium.ai/"
									target="_blank"
									rel="noreferrer"
									title="Instagram"
								>
									<Instagram className="h-6 w-6 text-white" />
								</a>
								<a
									href="https://www.linkedin.com/company/migranium/"
									target="_blank"
									rel="noreferrer"
									title="Linkedin"
								>
									<Linkedin className="h-6 w-6 text-white" />
								</a>
								<a
									href="https://twitter.com/migranium"
									target="_blank"
									rel="noreferrer"
									title="Twitter"
								>
									<SiX className="h-6 w-6 text-white" />
								</a>
							</ul>
						</div>
						<hr className="w-full border border-[#1D3E69]" />
						<div className="flex w-full items-center justify-between text-base text-white/70 mlg:flex-col-reverse">
							<p>
								Copyright {new Date().getFullYear()} &copy;
								migranium.com
							</p>
							<ul className="mb-12 flex flex-col items-center space-y-6 font-inter font-medium lg:mb-0 lg:flex-row lg:space-x-6 lg:space-y-0">
								<Link
									href={"/privacy-policy"}
									className="cursor-pointer duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Privacy Policy
								</Link>
								<Link
									href={"/terms"}
									className="cursor-pointer duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Terms of Service
								</Link>
								<Link
									href={"/payment-terms"}
									className="cursor-pointer duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Payment Terms
								</Link>
								<Link
									href={"/cookies-settings"}
									className="cursor-pointer duration-200 ease-in-out hover:text-[#72F4E8]"
								>
									Cookies Settings
								</Link>
							</ul>
						</div>
					</div>
				</div>
			</footer>
		);
	};

export default Footer;
