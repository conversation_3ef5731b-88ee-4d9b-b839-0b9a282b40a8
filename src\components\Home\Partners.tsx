import React from "react";
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";

export const Partners = () => {
	const partnerLogos = [
		{ src: "/assets/images/OH-logo.svg", alt: "Ontario Health" },
		{
			src: "/assets/images/UOW.svg",
			alt: "University of Waterloo",
		},
		{
			src: "/assets/images/NM.svg",
			alt: "Network Medicals",
		},
		{
			src: "/assets/images/NWT.svg",
			alt: "North Western Toronto",
		},
		{ src: "/assets/images/microsoft.svg", alt: "Microsoft" },
		{ src: "/assets/images/velocity.png", alt: "Velocity" },
	];

	const autoplay = React.useRef(
		Autoplay({
			delay: 2000,
			stopOnInteraction: false,
			stopOnMouseEnter: false,
		})
	);

	const [emblaRef] = useEmblaCarousel(
		{
			loop: true,
			align: "start",
			slidesToScroll: 1,
			containScroll: "trimSnaps",
		},
		[autoplay.current]
	);

	return (
		<section className="mt-[30px] w-full py-[52px] lg:mt-9 lg:py-12">
			<div className="hidden lg:flex lg:items-center lg:justify-center">
				<div className="flex flex-wrap items-center justify-center gap-[52.96px]">
					{partnerLogos.map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-8 w-auto flex-shrink-0 grayscale transition-all duration-300 hover:grayscale-0"
							style={{ maxWidth: 140 }}
						/>
					))}
				</div>
			</div>
			{/* Mobile */}
			<div className="overflow-hidden lg:hidden" ref={emblaRef}>
				<div className="flex">
					{[...partnerLogos, ...partnerLogos, ...partnerLogos].map(
						(logo, idx) => (
							<div key={idx} className="flex-[0_0_auto] px-3">
								<img
									src={logo.src}
									alt={logo.alt}
									className="h-8 w-auto flex-shrink-0 grayscale"
									style={{ maxWidth: 140 }}
								/>
							</div>
						)
					)}
				</div>
			</div>
		</section>
	);
};
