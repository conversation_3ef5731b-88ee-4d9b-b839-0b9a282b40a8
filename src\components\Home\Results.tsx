import React from "react";
import Image from "next/image";

const resultsData = [
	{
		icon: "/assets/images/smile.png",
		value: "90%",
		label: "Patient Satisfaction",
	},
	{
		icon: "/assets/images/Clock.png",
		value: "30%",
		label: "Administrative Time Saved",
	},
	{
		icon: "/assets/images/reduction.png",
		value: "20%",
		label: "Wait Time Reduction",
	},
	{
		icon: "/assets/images/revenue.png",
		value: "15%",
		label: "Increase in Revenue",
	},
];

export const Results = () => {
	return (
		<div id="impact" className="py- relative w-full font-inter md:py-24">
			<svg
				className="absolute inset-0 hidden h-full w-full sm:block"
				viewBox="0 0 1323 563"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M1 1.19531V562.195C1 562.195 415.327 482.195 683.801 482.195C935.4 482.195 1322 562.195 1322 562.195V1.19531C1322 1.19531 935.9 93.6953 683.801 93.6953C414.715 93.6953 1 1.19531 1 1.19531Z"
					fill="#032F57"
					stroke="black"
				/>
			</svg>
			<svg
				className="absolute inset-0 h-full w-full sm:hidden"
				width="600"
				height="1091"
				viewBox="0 0 600 1091"
				preserveAspectRatio="none"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M-0.462891 1V1090L2.47857 1088.57C215.065 1033.77 ************.19 599.537 1087V1C452.041 65.74 218.042 67.71 8.6908 6.15L-0.462891 1Z"
					fill="#032F57"
					stroke="black"
				/>
			</svg>
			<div className="relative z-10 flex max-h-full flex-col items-center justify-center py-28 lg:py-10">
				<div className="mx-auto mb-12 flex w-full max-w-[312px] flex-col items-center text-center sm:max-w-4xl">
					<span className="mb-2 font-shantell text-sm font-medium text-[#72F4E8] lg:text-lg">
						Results
					</span>
					<h2 className="text-xl font-bold text-white lg:text-4xl">
						Proven Impact, Measurable Outcomes
					</h2>
				</div>

				<div className="grid w-full max-w-[1214px] grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 lg:justify-between lg:gap-8">
					{resultsData.map((item, index) => (
						<div
							key={index}
							className={`flex flex-col items-center ${
								index < resultsData.length - 1
									? "justify-center border-b border-b-[#005893] pb-6 sm:w-auto sm:max-w-full sm:border-b-0 sm:border-r sm:border-r-[#005893] sm:pb-0 sm:pr-5 msm:mx-auto msm:w-[176px] msm:max-w-[176px]"
									: ""
							}`}
						>
							<div className="relative mb-4 h-8 w-8 md:h-12 md:w-12">
								<Image
									src={item.icon}
									alt={item.label}
									fill
									className="object-contain"
								/>
							</div>
							<div className="mb-2 text-[30px] font-semibold text-white lg:text-[40px]">
								{item.value}
							</div>
							<div className="text-sm text-white lg:text-base">
								{item.label}
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
