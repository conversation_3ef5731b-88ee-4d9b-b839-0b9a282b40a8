"use client";

import ContactUsModal from "@/components/ContactUsModal";
import Footer from "@/components/Footer";
import { GetInTouchModal } from "@/components/GetInTouchModal";
import BookADemo from "@/components/Home/BookADemo";
import { Complaints } from "@/components/Home/Complaints";
import CoreFeatures from "@/components/Home/CoreFeatures/CoreFeatures";
import { Features } from "@/components/Home/Features/Features";
import { GetInTouch } from "@/components/Home/GetInTouch";
import HeroSection from "@/components/Home/HeroSection";
import HoursSaved from "@/components/Home/HoursSaved";
import HowItWorks from "@/components/Home/HowItWorks/HowItWorks";
import { Interactions } from "@/components/Home/Interactions";
import { Difference } from "@/components/Home/MakesUsDifferent/Difference";
import Navbar from "@/components/Home/Navbar";
import OptimiseOperations from "@/components/Home/OptimiseOperations";
import { Partners } from "@/components/Home/Partners";
import { Results } from "@/components/Home/Results";
import Subscribe from "@/components/Home/Subscribe";
import { Testimonial } from "@/components/Home/Testimonial";
import WhatUsersSay from "@/components/Home/WhatUsersSay";
import { NextPage } from "next";
import { useState } from "react";

const Home: NextPage = () => {
	const [showBookADemo, setShowBookDemo] = useState(false);
	const [showContactUsModal, setshowContactUsModal] = useState(false);
	return (
		<>
			<main className="">
				<Navbar
					showBookADemo={showBookADemo}
					setShowBookDemo={setShowBookDemo}
					showContactUsModal={showContactUsModal}
					setshowContactUsModal={setshowContactUsModal}
				/>
				<HeroSection setShowBookDemo={setShowBookDemo} />
				<Partners />
				<Features />
				<Interactions />
				<Difference />
				<Complaints />
				<Results />
				<Testimonial />
				<GetInTouch setshowContactUsModal={setshowContactUsModal} />
				<Footer />
				{/* <CoreFeatures /> */}
				{/* <HoursSaved />
				<HowItWorks setShowBookDemo={setShowBookDemo} />
				<Subscribe />
				<WhatUsersSay />
				<OptimiseOperations
					setShowBookDemo={setShowBookDemo}
					setshowContactUsModal={setshowContactUsModal}
				/>
				<Footer /> */}
			</main>
			<GetInTouchModal
				show={showContactUsModal}
				setShow={setshowContactUsModal}
			/>
			<BookADemo
				showBookADemo={showBookADemo}
				setShowBookDemo={setShowBookDemo}
			/>
			{/* <ContactUsModal
				show={showContactUsModal}
				setShow={setshowContactUsModal}
			/>
			<BookADemo
				showBookADemo={showBookADemo}
				setShowBookDemo={setShowBookDemo}
			/> */}
		</>
	);
};

export default Home;
